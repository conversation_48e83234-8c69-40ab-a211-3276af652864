# Ecommerce Database Setup

This project contains a comprehensive ecommerce database schema with PostgreSQL and Docker setup.

## Quick Start

### 1. Start the Database

```bash
# Start PostgreSQL and PgAdmin
docker-compose up -d

# Check if services are running
docker-compose ps
```

### 2. Setup Prisma

```bash
# Install Prisma CLI (if not already installed)
npm install -g prisma

# Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push

# (Optional) Open Prisma Studio to view data
npx prisma studio
```

## Services

### PostgreSQL Database
- **Host:** localhost
- **Port:** 5432
- **Database:** ecommerce
- **Username:** postgres
- **Password:** password123
- **Connection String:** `postgresql://postgres:password123@localhost:5432/ecommerce?schema=public`

### PgAdmin (Database Management)
- **URL:** http://localhost:8080
- **Email:** <EMAIL>
- **Password:** admin123

## Database Schema

The schema includes 25 tables covering:

- **User Management:** Users, Addresses
- **Product Catalog:** Products, Categories, Brands, Variants, Images
- **Inventory:** Stock tracking across warehouses
- **Shopping:** Cart items, Wishlists
- **Orders:** Order management and items
- **Payments:** Payment processing and methods
- **Shipping:** Shipment tracking
- **Marketing:** Discounts, Reviews, Newsletter
- **Analytics:** Page views and settings

## Useful Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs postgres
docker-compose logs pgadmin

# Reset database (removes all data)
docker-compose down -v
docker-compose up -d

# Backup database
docker exec ecommerce_db pg_dump -U postgres ecommerce > backup.sql

# Restore database
docker exec -i ecommerce_db psql -U postgres ecommerce < backup.sql
```

## Environment Variables

Copy `.env.example` to `.env` and modify as needed:

```bash
cp .env.example .env
```

## Troubleshooting

### Port Already in Use
If port 5432 is already in use, change it in `docker-compose.yml`:
```yaml
ports:
  - "5433:5432"  # Use port 5433 instead
```

Then update your `DATABASE_URL` in `.env`:
```
DATABASE_URL="postgresql://postgres:password123@localhost:5433/ecommerce?schema=public"
```

### Connection Issues
1. Ensure Docker is running
2. Check if containers are healthy: `docker-compose ps`
3. View logs: `docker-compose logs postgres`
4. Test connection: `docker exec ecommerce_db psql -U postgres -d ecommerce -c "SELECT 1;"`
