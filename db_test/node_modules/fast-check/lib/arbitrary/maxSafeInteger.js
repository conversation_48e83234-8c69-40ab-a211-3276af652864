"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.maxSafeInteger = maxSafeInteger;
const IntegerArbitrary_1 = require("./_internals/IntegerArbitrary");
const safeMinSafeInteger = Number.MIN_SAFE_INTEGER;
const safeMaxSafeInteger = Number.MAX_SAFE_INTEGER;
function maxSafeInteger() {
    return new IntegerArbitrary_1.IntegerArbitrary(safeMinSafeInteger, safeMaxSafeInteger);
}
