# `effect` Core Package

The `effect` package is the heart of the Effect framework, providing robust primitives for managing side effects, ensuring type safety, and supporting concurrency in your TypeScript applications.

## Requirements

- **TypeScript 5.4 or Newer:**
  Ensure you are using a compatible TypeScript version.

- **Strict Type-Checking:**
  The `strict` flag must be enabled in your `tsconfig.json`. For example:

  ```json
  {
    "compilerOptions": {
      "strict": true
      // ...other options
    }
  }
  ```

## Installation

Install the core package using your preferred package manager. For example, with npm:

```bash
npm install effect
```

## Documentation

- **Website:**
  For detailed information and usage examples, visit the [Effect website](https://www.effect.website/).

- **API Reference:**
  For a complete API reference of the core package `effect`, see the [Effect API documentation](https://effect-ts.github.io/effect/).

## Overview of Effect Modules

The `effect` package provides a collection of modules designed for functional programming in TypeScript. Below is a brief overview of the core modules:

| Module   | Description                                                                                                                |
| -------- | -------------------------------------------------------------------------------------------------------------------------- |
| Effect   | The core abstraction for managing side effects, concurrency, and error handling in a structured way.                       |
| Context  | A lightweight dependency injection mechanism that enables passing services through computations without direct references. |
| Layer    | A system for managing dependencies, allowing for modular and composable resource allocation.                               |
| Fiber    | Lightweight virtual threads with resource-safe cancellation capabilities, enabling many features in Effect.                |
| Stream   | A powerful abstraction for handling asynchronous, event-driven data processing.                                            |
| Schedule | A module for defining retry and repeat policies with composable schedules.                                                 |
| Scope    | Manages the lifecycle of resources, ensuring proper acquisition and release.                                               |
| Schema   | A powerful library for defining, validating, and transforming structured data with type-safe encoding and decoding.        |

For a comparison between `effect/Schema` and `zod`, see [Schema vs Zod](https://github.com/Effect-TS/effect/tree/main/packages/effect/schema-vs-zod.md).
