import type * as Schema from "../../Schema.js"

/** @internal */
export const DateFromSelfSchemaId: Schema.DateFromSelfSchemaId = Symbol.for(
  "effect/SchemaId/DateFromSelf"
) as Schema.DateFromSelfSchemaId

/** @internal */
export const GreaterThanSchemaId: Schema.GreaterThanSchemaId = Symbol.for(
  "effect/SchemaId/GreaterThan"
) as Schema.GreaterThanSchemaId

/** @internal */
export const GreaterThanOrEqualToSchemaId: Schema.GreaterThanOrEqualToSchemaId = Symbol.for(
  "effect/SchemaId/GreaterThanOrEqualTo"
) as Schema.GreaterThanOrEqualToSchemaId

/** @internal */
export const LessThanSchemaId: Schema.LessThanSchemaId = Symbol.for(
  "effect/SchemaId/LessThan"
) as Schema.LessThanSchemaId

/** @internal */
export const LessThanOrEqualToSchemaId: Schema.LessThanOrEqualToSchemaId = Symbol.for(
  "effect/SchemaId/LessThanOrEqualTo"
) as Schema.LessThanOrEqualToSchemaId

/** @internal */
export const IntSchemaId: Schema.IntSchemaId = Symbol.for(
  "effect/SchemaId/Int"
) as Schema.IntSchemaId

/** @internal */
export const NonNaNSchemaId: Schema.NonNaNSchemaId = Symbol.for(
  "effect/SchemaId/NonNaN"
) as Schema.NonNaNSchemaId

/** @internal */
export const FiniteSchemaId: Schema.FiniteSchemaId = Symbol.for(
  "effect/SchemaId/Finite"
) as Schema.FiniteSchemaId

/** @internal */
export const JsonNumberSchemaId: Schema.JsonNumberSchemaId = Symbol.for(
  "effect/SchemaId/JsonNumber"
) as Schema.JsonNumberSchemaId

/** @internal */
export const BetweenSchemaId: Schema.BetweenSchemaId = Symbol.for(
  "effect/SchemaId/Between"
) as Schema.BetweenSchemaId

/** @internal */
export const GreaterThanBigintSchemaId: Schema.GreaterThanBigIntSchemaId = Symbol.for(
  "effect/SchemaId/GreaterThanBigint"
) as Schema.GreaterThanBigIntSchemaId

/** @internal */
export const GreaterThanOrEqualToBigIntSchemaId: Schema.GreaterThanOrEqualToBigIntSchemaId = Symbol.for(
  "effect/SchemaId/GreaterThanOrEqualToBigint"
) as Schema.GreaterThanOrEqualToBigIntSchemaId

/** @internal */
export const LessThanBigIntSchemaId: Schema.LessThanBigIntSchemaId = Symbol.for(
  "effect/SchemaId/LessThanBigint"
) as Schema.LessThanBigIntSchemaId

/** @internal */
export const LessThanOrEqualToBigIntSchemaId: Schema.LessThanOrEqualToBigIntSchemaId = Symbol.for(
  "effect/SchemaId/LessThanOrEqualToBigint"
) as Schema.LessThanOrEqualToBigIntSchemaId

/** @internal */
export const BetweenBigintSchemaId: Schema.BetweenBigIntSchemaId = Symbol.for(
  "effect/SchemaId/BetweenBigint"
) as Schema.BetweenBigIntSchemaId

/** @internal */
export const MinLengthSchemaId: Schema.MinLengthSchemaId = Symbol.for(
  "effect/SchemaId/MinLength"
) as Schema.MinLengthSchemaId

/** @internal */
export const MaxLengthSchemaId: Schema.MaxLengthSchemaId = Symbol.for(
  "effect/SchemaId/MaxLength"
) as Schema.MaxLengthSchemaId

/** @internal */
export const LengthSchemaId: Schema.LengthSchemaId = Symbol.for(
  "effect/SchemaId/Length"
) as Schema.LengthSchemaId

/** @internal */
export const MinItemsSchemaId: Schema.MinItemsSchemaId = Symbol.for(
  "effect/SchemaId/MinItems"
) as Schema.MinItemsSchemaId

/** @internal */
export const MaxItemsSchemaId: Schema.MaxItemsSchemaId = Symbol.for(
  "effect/SchemaId/MaxItems"
) as Schema.MaxItemsSchemaId

/** @internal */
export const ItemsCountSchemaId: Schema.ItemsCountSchemaId = Symbol.for(
  "effect/SchemaId/ItemsCount"
) as Schema.ItemsCountSchemaId
